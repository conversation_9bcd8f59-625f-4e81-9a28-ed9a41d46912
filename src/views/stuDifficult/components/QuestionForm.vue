<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-07-02 14:45:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 19:16:38
-->
<template>
  <BasicPopup v-bind="$attrs" @register="registerPopup" title="问卷调查" @ok="handleSubmit" showOkBtn cancelText="关闭" okText="保存">
    <Questionnaire ref="questionnaire" :opType="opType" v-if="opType" />
  </BasicPopup>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicPopup, usePopupInner } from '@/components/Popup';
  import Questionnaire from './Questionnaire.vue';
  const emit = defineEmits(['register', 'reload', 'finish']);

  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(init);
  const id = ref('');
  const opType = ref('');
  function init(data) {
    changeLoading(true);
    console.log(data.opType);
    id.value = data.id;
    opType.value = data.opType ? data.opType : 1;
    changeLoading(false);
  }
  const questionnaire = ref();
  async function handleSubmit() {
    const values = await questionnaire.value?.handleSubmit();
    if (!values) return;
    changeOkLoading(true);
    closePopup();
    emit('finish');
    changeOkLoading(false);
  }
</script>
