<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-07-02 14:45:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 20:25:39
-->
<template>
  <a-row class="mb-100px">
    <a-col :span="14" :offset="5">
      <Spin :spinning="spinning">
        <Mtcn-Alert v-if="props.opType === 1" showIcon message="必须答完所有题目才能保存哦，请勿中途退出！"> </Mtcn-Alert>
        <BasicForm @register="registerForm" class="mt-20px"> </BasicForm>
      </Spin>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import { RangePicker, Spin } from 'ant-design-vue';
  const props = defineProps({
    opType: {
      type: Number,
      default: 1, //1 学生 2 管理员
    },
  });
  const emit = defineEmits(['register', 'reload']);
  const id = ref('');
  const spinning = ref(false);
  const baseStore = useBaseStore();
  const api = useBaseApi('/api/knsPdxx');

  const { createMessage } = useMessage();
  const formArr = ref([]);
  const familyMembersForm = ref({});
  const formRefs = ref({});

  const schemas = ref([]);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    schemas: schemas.value,
    layout: 'vertical',
    labelWidth: '100%',
  });

  async function initForm() {
    spinning.value = true;
    const { data } = await api.request('get', '/api/knsDcwj/getDcwjList', { isFullPath: true });
    (data?.tmVoList || []).forEach((item, index) => {
      let options = {};
      if (item.tmlx == 1 || item.tmlx == 2) {
        const optionItem = item.xxList.map(item => {
          return {
            fullName: item.xxmc,
            enCode: item.xxdm,
            tmdm: item.tmdm,
            fz: item.fz,
          };
        });
        options = {
          options: optionItem,
          fieldNames: { label: 'fullName', value: 'enCode' },
        };
      }
      schemas.value.push({
        field: `${item.wjdm}_${item.tmdm}_${item.fz}`,
        label: `${index + 1}、${item.tmmc}`,
        component: item.tmtxfslx || 'Input',
        componentProps: { placeholder: item.tmlx == 3 ? '请输入' : '请选择', ...options, disabled: false },
        rules: [
          {
            required: item.sfbt || false,
            trigger: 'blur',
            message: `${item.tmlx == 3 ? '请输入' : '请选择'}`,
            type: { 1: 'string', 2: 'array', 3: 'string' }[item.tmlx],
          },
        ],
      });
    });
    // updateSchema(schemas.value)
    spinning.value = false;
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return false;
    for (let item of values) {
    }
    const data = ;
    await api.request('post', '/api/knsDcwjDt/save', { params: values, isFullPath: true });

    return values;
  }

  defineExpose({
    handleSubmit,
  });
  onMounted(() => {
    initForm();
    if (props.opType === 2) {
      const updates = schemas.value.map((item, index) => ({
        field: item.field,
        componentProps: {
          ...item.componentProps,
          disabled: index !== 0,
        },
      }));
      updateSchema(updates);
    }
  });
</script>
