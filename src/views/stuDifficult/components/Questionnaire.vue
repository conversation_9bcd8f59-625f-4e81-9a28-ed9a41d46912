<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-07-02 14:45:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 20:27:47
-->
<template>
  <a-row class="mb-100px">
    <a-col :span="14" :offset="5">
      <Spin :spinning="spinning">
        <Mtcn-Alert v-if="props.opType === 1" showIcon message="必须答完所有题目才能保存哦，请勿中途退出！"> </Mtcn-Alert>
        <BasicForm @register="registerForm" class="mt-20px"> </BasicForm>
      </Spin>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, onMounted } from 'vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import { RangePicker, Spin } from 'ant-design-vue';
  const props = defineProps({
    opType: {
      type: Number,
      default: 1, //1 学生 2 管理员
    },
  });
  const emit = defineEmits(['register', 'reload']);
  const id = ref('');
  const spinning = ref(false);
  const baseStore = useBaseStore();
  const api = useBaseApi('/api/knsPdxx');

  const { createMessage } = useMessage();
  const formArr = ref([]);
  const familyMembersForm = ref({});
  const formRefs = ref({});

  const schemas = ref([]);

  const [registerForm, { setFieldsValue, resetFields, validate, updateSchema }] = useForm({
    schemas: schemas.value,
    layout: 'vertical',
    labelWidth: '100%',
  });

  async function initForm() {
    spinning.value = true;
    const { data } = await api.request('get', '/api/knsDcwj/getDcwjList', { isFullPath: true });
    (data?.tmVoList || []).forEach((item, index) => {
      let options = {};
      if (item.tmlx == 1 || item.tmlx == 2) {
        const optionItem = item.xxList.map(item => {
          return {
            fullName: item.xxmc,
            enCode: item.xxdm,
            tmdm: item.tmdm,
            fz: item.fz,
          };
        });
        options = {
          options: optionItem,
          fieldNames: { label: 'fullName', value: 'enCode' },
        };
      }
      schemas.value.push({
        field: `${item.wjdm}_${item.tmdm}`,
        label: `${index + 1}、${item.tmmc}`,
        component: item.tmtxfslx || 'Input',
        componentProps: { placeholder: item.tmlx == 3 ? '请输入' : '请选择', ...options, disabled: false },
        rules: [
          {
            required: item.sfbt || false,
            trigger: 'blur',
            message: `${item.tmlx == 3 ? '请输入' : '请选择'}`,
            type: { 1: 'string', 2: 'array', 3: 'string' }[item.tmlx],
          },
        ],
      });
    });
    // updateSchema(schemas.value)
    spinning.value = false;
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return false;

    // 组装保存数据
    const data = assembleSubmitData(values);
    await api.request('post', '/api/knsDcwjDt/save', { params: data, isFullPath: true });

    return values;
  }

  /**
   * 组装提交数据
   * @param values 表单验证后的数据，格式如：{ "**********_TM001_1": "A", "**********_TM002_2": ["B", "C"] }
   * @returns 组装后的数据，格式如：{ wjdm: '**********', dtmxVoList: [...] }
   */
  function assembleSubmitData(values) {
    const dtmxVoList = [];
    let wjdm = '';

    // 遍历表单数据
    Object.keys(values).forEach(fieldKey => {
      const fieldValue = values[fieldKey];

      // 解析字段名：格式为 ${wjdm}_${tmdm}_${fz}
      const fieldParts = fieldKey.split('_');
      if (fieldParts.length >= 3) {
        const currentWjdm = fieldParts[0];
        const tmdm = fieldParts[1];
        const fz = fieldParts[2];

        // 设置问卷代码（取第一个即可，因为同一份问卷的wjdm都相同）
        if (!wjdm) {
          wjdm = currentWjdm;
        }

        // 根据题目类型处理答案
        if (Array.isArray(fieldValue)) {
          // 多选题：每个选项创建一条记录
          fieldValue.forEach(selectedValue => {
            // 从schemas中找到对应的选项信息
            const optionInfo = findOptionInfo(tmdm, selectedValue);
            dtmxVoList.push({
              xxdm: selectedValue,
              tmdm: tmdm,
              qtda: optionInfo?.qtda || selectedValue, // 如果找不到选项信息，使用选择的值作为其他答案
              fz: optionInfo?.fz || fz,
            });
          });
        } else if (fieldValue) {
          // 单选题或简答题：创建一条记录
          const optionInfo = findOptionInfo(tmdm, fieldValue);
          dtmxVoList.push({
            xxdm: fieldValue,
            tmdm: tmdm,
            qtda: optionInfo?.qtda || fieldValue, // 简答题的答案或选项答案
            fz: optionInfo?.fz || fz,
          });
        }
      }
    });

    return {
      wjdm,
      dtmxVoList,
    };
  }

  /**
   * 从schemas中查找选项信息
   * @param tmdm 题目代码
   * @param selectedValue 选择的值
   * @returns 选项信息
   */
  function findOptionInfo(tmdm, selectedValue) {
    // 遍历schemas找到对应题目的选项
    for (const schema of schemas.value) {
      if (schema.componentProps?.options) {
        const option = schema.componentProps.options.find(opt => opt.enCode === selectedValue && opt.tmdm === tmdm);
        if (option) {
          return {
            qtda: selectedValue, // 选项代码作为其他答案
            fz: option.fz,
          };
        }
      }
    }
    return null;
  }

  defineExpose({
    handleSubmit,
  });
  onMounted(() => {
    initForm();
    if (props.opType === 2) {
      const updates = schemas.value.map((item, index) => ({
        field: item.field,
        componentProps: {
          ...item.componentProps,
          disabled: index !== 0,
        },
      }));
      updateSchema(updates);
    }
  });
</script>
