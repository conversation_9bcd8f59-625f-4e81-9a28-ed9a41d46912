<!--
 * @Description: 添加题目
 * @Autor: Fhz
 * @Date: 2025-04-21 16:38:40
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 20:22:52
-->

<template>
  <BasicModal
    v-bind="$attrs"
    class="transfer-modal member-modal"
    @register="registerModal"
    :title="`${!id ? '创建' : '编辑'}题目`"
    showOkBtn
    @ok="handleSubmit">
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, toRefs, unref, computed, reactive, onMounted } from 'vue';
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { addAloneField } from '@/api/serviceMatters/ItemFieldsConfig';
  import { getDictionaryTypeSelector } from '@/api/systemData/dictionary';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const emit = defineEmits(['register', 'reload']);
  const api = useBaseApi('/api/knsDcwjTm');
  const baseStore = useBaseStore();
  const id = ref('');
  const wjdm = ref('');
  const { createMessage } = useMessage();
  const schemasForm: FormSchema[] = [
    {
      field: 'tmlx',
      label: '题目类型',
      component: 'Select',
      componentProps: {
        options: [
          { id: '1', fullName: '单选题' },
          { id: '2', fullName: '多选题' },
          { id: '3', fullName: '简答题' },
        ],
      },
      rules: [{ required: true, trigger: 'blur', message: '题目类型不能为空' }],
    },
    {
      field: 'tmmc',
      label: '题目',
      component: 'Textarea',
      componentProps: { placeholder: '输入题目名称' },
      rules: [{ required: true, trigger: 'blur', message: '题目名称不能为空' }],
    },
    {
      field: 'tmtxfslx',
      label: '类型',
      component: 'Select',
      defaultValue: 'Input',
      componentProps: {
        allowClear: false,
        options: [
          { id: 'Input', fullName: '文本输入框Input' },
          { id: 'Radio', fullName: '单选Radio' },
          { id: 'Checkbox', fullName: '多选Checkbox' },
          { id: 'Select', fullName: '选择框Select' },
          { id: 'Textarea', fullName: '多行文本输入框Textarea' },
          { id: 'Editor', fullName: '富文本Editor' },
          { id: 'UploadImgSingle', fullName: '图片Image' },
          { id: 'UploadFile', fullName: '文件File' },
        ],
      },
      rules: [{ required: true, trigger: 'blur' }],
    },

    // {
    //   field: 'msmfSource',
    //   label: '数据字典',
    //   component: 'TreeSelect',
    //   rules: [{ required: true, trigger: 'blur' }],
    //   ifShow: ({ values }) => {
    //     return ['Radio', 'Checkbox', 'Select'].includes(values.tmtxfslx);
    //   },
    // },
    {
      label: '是否必填',
      field: 'sfbt',
      component: 'Switch',
      defaultValue: 0,
      rules: [{ required: true, trigger: 'blur' }],
    },
    {
      label: '使用分值',
      field: 'syfz',
      component: 'Switch',
      defaultValue: 0,
      rules: [{ required: true, trigger: 'blur' }],
    },
    {
      label: '题目分值',
      field: 'fz',
      component: 'InputNumber',
      defaultValue: 0,
      componentProps: { min: '0', max: '999999', precision: 0, placeholder: '请输入' },
      rules: [{ required: true, trigger: 'blur' }],
      ifShow: ({ values }) => {
        return values.tmlx == 3 && values.syfz;
      },
    },
    {
      label: '排序',
      field: 'sortCode',
      component: 'InputNumber',
      componentProps: { min: '0', max: '999999', placeholder: '请输入' },
    },
  ];

  const [registerForm, { setFieldsValue, getFieldsValue, validate, resetFields, updateSchema }] = useForm({ labelWidth: 100, schemas: schemasForm });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const [registerAddForm, { openModal: openAddFormModal }] = useModal();
  async function init(data) {
    id.value = data?.id;
    wjdm.value = data?.wjdm;
    changeLoading(true);
    resetFields();
    getDicOptions();
    updateSchema({
      field: 'tmlx',
      componentProps: { disabled: data.id ? true : false },
    });
    if (data.id) {
      const { data: resData } = await api.getDetail({ params: { id: data.id } });
      resData.sfbt = Number(resData.sfbt || 0);
      resData.syfz = Number(resData.syfz || 0);
      setFieldsValue(resData);
    }
    changeLoading(false);
  }
  async function handleSubmit() {
    const values = await validate();
    if (!values) return;
    changeOkLoading(true);

    const query = {
      ...values,
      wjdm: wjdm.value,
      id: id.value,
    };

    const res = id.value ? await api.edit({ params: query }) : await api.save({ params: query });
    createMessage.success(res.msg);
    changeOkLoading(false);
    emit('reload');
    closeModal();
  }

  function getDicOptions() {
    getDictionaryTypeSelector().then(res => {
      updateSchema({
        field: 'msmfSource',
        componentProps: { options: res.data.list, fieldNames: { label: 'fullName', value: 'enCode' } },
      });
    });
  }
</script>
